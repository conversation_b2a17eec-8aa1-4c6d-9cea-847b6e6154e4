import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { AppConfig } from 'src/app/configs/app.config';
import { LoggerService } from '../../interceptors/logger.service';
import { ProgressService } from '../../services/progress.service';
import { AnonymousPaymentService } from '../../services/anonymous-payment.service';
import { AnonymousReceiptData } from '../../models/anonymous-payment.model';

@Component({
  selector: 'app-anonymous-receipt',
  templateUrl: './anonymous-receipt.component.html',
  styleUrls: ['./anonymous-receipt.component.css']
})
export class AnonymousReceiptComponent implements OnInit, OnDestroy {
  private logger: LoggerService;
  private routeSubscription: Subscription;
  private receiptSubscription: Subscription;

  imageFolderPath: string = AppConfig.imageFolderPath;
  bookingId: string;
  receiptData: AnonymousReceiptData;
  isLoading: boolean = true;
  hasError: boolean = false;
  errorMessage: string = '';

  // Cache for calculated drop-off date/time
  private _dropOffDateTime: { date: string, time: string } | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private anonymousPaymentService: AnonymousPaymentService
  ) {
    this.logger = LoggerService.createLogger('AnonymousReceiptComponent');
  }

  ngOnInit(): void {
    this.logger.trace('AnonymousReceiptComponent:: ngOnInit() called');
    this.routeSubscription = this.route.params.subscribe(params => {
      // First try to get ID from path parameter (/receipt/:id)
      this.bookingId = params['id'];

      // If no path parameter, try query parameter (/receipt/?id=xxx)
      if (!this.bookingId) {
        this.bookingId = this.route.snapshot.queryParams['id'];
      }

      if (this.bookingId) {
        this.loadReceiptData();
      } else {
        this.handleError('Invalid booking ID');
      }
    });
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.receiptSubscription) {
      this.receiptSubscription.unsubscribe();
    }
  }

  private loadReceiptData(): void {
    this.isLoading = true;
    this.showProgressBar();

    this.receiptSubscription = this.anonymousPaymentService.getPaymentReceipt(this.bookingId)
      .subscribe(
        (response) => {
          this.hideProgressBar();
          this.isLoading = false;

          if (response.succeeded && response.data) {
            this.receiptData = response.data;
            // Reset cache when new data is loaded
            this._dropOffDateTime = null;
          } else {
            this.handleError(response.message || 'Failed to load receipt details');
          }
        },
        (error) => {
          this.hideProgressBar();
          this.isLoading = false;
          this.logger.error('Error loading receipt data:', error);
          this.handleError('Unable to load receipt details. Please try again later.');
        }
      );
  }

  navigateHome(): void {
    this.router.navigate(['/home']);
  }

  downloadReceipt(): void {
    // Implementation for downloading receipt as PDF
    this.toastrService.info('Receipt download feature will be available soon');
  }

  shareReceipt(): void {
    // Implementation for sharing receipt
    if ((navigator as any).share) {
      (navigator as any).share({
        title: 'CabYaari Booking Receipt',
        text: `Booking ID: ${this.bookingId}`,
        url: window.location.href
      }).catch((error: any) => {
        this.logger.error('Error sharing receipt:', error);
        this.copyReceiptLink();
      });
    } else {
      this.copyReceiptLink();
    }
  }

  private copyReceiptLink(): void {
    const receiptUrl = window.location.href;
    if (navigator.clipboard) {
      navigator.clipboard.writeText(receiptUrl).then(() => {
        this.toastrService.success('Receipt link copied to clipboard');
      }).catch(() => {
        this.fallbackCopyTextToClipboard(receiptUrl);
      });
    } else {
      this.fallbackCopyTextToClipboard(receiptUrl);
    }
  }

  private fallbackCopyTextToClipboard(text: string): void {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        this.toastrService.success('Receipt link copied to clipboard');
      } else {
        this.toastrService.error('Failed to copy receipt link');
      }
    } catch (err) {
      this.toastrService.error('Failed to copy receipt link');
    }
    
    document.body.removeChild(textArea);
  }

  private handleError(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    this.toastrService.error(message);
  }

  private showProgressBar(): void {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar(): void {
    this.progressService.isPorgress.next(false);
  }

  // Utility methods for template
  isPaymentCompleted(): boolean {
    return this.receiptData?.paymentStatus === 'COMPLETED';
  }

  isPaymentPending(): boolean {
    return this.receiptData?.paymentStatus === 'PENDING';
  }

  getPaymentStatusText(): string {
    if (!this.receiptData) return '';
    
    switch (this.receiptData.paymentStatus) {
      case 'COMPLETED':
        return 'Payment Completed Successfully';
      case 'PENDING':
        return 'Payment Pending';
      case 'FAILED':
        return 'Payment Failed';
      case 'CANCELLED':
        return 'Payment Cancelled';
      default:
        return 'Payment Status Unknown';
    }
  }

  getPaymentStatusIcon(): string {
    if (!this.receiptData) return 'fa-question-circle';
    
    switch (this.receiptData.paymentStatus) {
      case 'COMPLETED':
        return 'fa-check-circle';
      case 'PENDING':
        return 'fa-clock';
      case 'FAILED':
        return 'fa-times-circle';
      case 'CANCELLED':
        return 'fa-ban';
      default:
        return 'fa-question-circle';
    }
  }

  getPaymentStatusColor(): string {
    if (!this.receiptData) return '#6c757d';
    
    switch (this.receiptData.paymentStatus) {
      case 'COMPLETED':
        return '#28a745';
      case 'PENDING':
        return '#ffc107';
      case 'FAILED':
        return '#dc3545';
      case 'CANCELLED':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  }

  calculateDropOffDateTime(): { date: string, time: string } {
    // Return cached result if available
    if (this._dropOffDateTime) {
      return this._dropOffDateTime;
    }

    if (!this.receiptData?.pickUpDate || !this.receiptData?.pickUpTime || !this.receiptData?.duration) {
      return { date: '', time: '' };
    }

    try {
      // Parse duration to extract hours and minutes
      const durationMatch = this.receiptData.duration.match(/(\d+)\s*hrs?\s*(\d+)?\s*mins?/i);
      if (!durationMatch) return { date: '', time: '' };

      const durationHours = parseInt(durationMatch[1]) || 0;
      const durationMinutes = parseInt(durationMatch[2]) || 0;

      // Parse pickup date and time
      const pickupDate = new Date(this.receiptData.pickUpDate);
      const pickupTime = this.receiptData.pickUpTime;

      // Handle different time formats (24-hour or 12-hour)
      let pickupDateTime: Date;

      if (pickupTime.includes('AM') || pickupTime.includes('PM')) {
        // 12-hour format with AM/PM
        const [time, period] = pickupTime.split(' ');
        const [hours, minutes] = time.split(':').map(Number);

        let hour24 = hours;
        if (period === 'PM' && hours !== 12) hour24 += 12;
        if (period === 'AM' && hours === 12) hour24 = 0;

        pickupDateTime = new Date(pickupDate);
        pickupDateTime.setHours(hour24, minutes, 0, 0);
      } else {
        // 24-hour format
        const [hours, minutes] = pickupTime.split(':').map(Number);
        pickupDateTime = new Date(pickupDate);
        pickupDateTime.setHours(hours, minutes, 0, 0);
      }

      // Add duration to pickup time
      const dropOffDateTime = new Date(pickupDateTime);
      dropOffDateTime.setHours(dropOffDateTime.getHours() + durationHours);
      dropOffDateTime.setMinutes(dropOffDateTime.getMinutes() + durationMinutes);

      // Format drop-off date
      const dropOffDate = dropOffDateTime.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Format drop-off time in 12-hour format
      const dropOffTime = dropOffDateTime.toLocaleTimeString('en-IN', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });

      // Cache the result
      this._dropOffDateTime = { date: dropOffDate, time: dropOffTime };
      return this._dropOffDateTime;
    } catch (error) {
      this.logger.error('Error calculating drop-off date/time:', error);
      return { date: '', time: '' };
    }
  }

  getFormattedPickupTime(): string {
    if (!this.receiptData?.pickUpTime) return '';

    try {
      const pickupTime = this.receiptData.pickUpTime;

      // If already in AM/PM format, return as is
      if (pickupTime.includes('AM') || pickupTime.includes('PM')) {
        return pickupTime;
      }

      // Convert 24-hour to 12-hour format
      const [hours, minutes] = pickupTime.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours === 0 ? 12 : (hours > 12 ? hours - 12 : hours);

      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (error) {
      this.logger.error('Error formatting pickup time:', error);
      return this.receiptData.pickUpTime;
    }
  }

  formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  }

  formatDateTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  }
}
