<app-progress-loader></app-progress-loader>

<!-- Error State -->
<div *ngIf="hasError" class="error-container">
    <header id="header" class="smallheader">
        <span class="toggle">
            <a (click)="navigateHome()" class="fa fa-arrow-left" style="cursor: pointer;"></a>
        </span>
        <div class="topheader">
            <a routerLink="/home" class="logo">
                <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
            </a>
        </div>
        <div class="forminner">
            <div class="head"><span></span>Payment Error</div>
            <div class="error-message">
                <i class="fa fa-exclamation-triangle" style="color: #ff6b6b; font-size: 2em; margin-bottom: 1rem;"></i>
                <h3>{{ errorMessage }}</h3>
                <button class="btn btn-primary mt-3" (click)="navigateHome()">Go to Home</button>
            </div>
        </div>
    </header>
</div>

<!-- Loading State -->
<div *ngIf="isLoading && !hasError" class="loading-container">
    <header id="header" class="smallheader">
        <div class="topheader">
            <a routerLink="/home" class="logo">
                <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
            </a>
        </div>
        <div class="forminner">
            <div class="head"><span></span>Loading Payment Details</div>
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-3">Please wait while we load your booking details...</p>
            </div>
        </div>
    </header>
</div>

<!-- Main Payment Page -->
<div *ngIf="!isLoading && !hasError && bookingDetails">
    <header id="header" class="smallheader">
        <div class="topheader">
            <a routerLink="/home" class="logo">
                <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
            </a>
        </div>

        <div class="forminner">
            <div class="head"><span></span>Payment Option</div>
            <div class="payment-option">
                <!-- Partial Payment Option (only show if partial payment is available) -->
                <div class="payment-box selected"
                     *ngIf="isPartialPaymentAvailable">
                    <label class="payment-label">
                        <div class="payment-check">
                            <label class="custom-checkbox">
                                <input type="checkbox" checked disabled>
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="payment-rate">
                            <span class="token-amount">Pay ₹ <span>{{ getPartialPaymentAmount() }}</span></span>
                            <span class="driver-pay">Pay rest to Driver</span>
                        </div>
                    </label>
                </div>

                <!-- Full Payment Option (only show if partial payment is NOT available) -->
                <div class="payment-box selected"
                     *ngIf="!isPartialPaymentAvailable">
                    <label class="payment-label">
                        <div class="payment-check">
                            <label class="custom-checkbox">
                                <input type="checkbox" checked disabled>
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="payment-rate">
                            <span class="token-amount">Pay <span>100%</span></span>
                            <span class="driver-pay">Pay the full amount</span>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <div class="forminner mt-4">
            <div class="fare-table">
                <div class="fare-heading">
                    <span>Total Fare</span>
                    <span class="t-right">₹ {{bookingDetails.totalFare}}</span>
                </div>

                <div class="amount-payable">
                    <span>Amount Payable Online</span>
                    <span class="t-right">₹ {{amountToPay}}</span>
                </div>

                <div class="driver-payable">
                    <span>Cash to pay Driver</span>
                    <span class="t-right">₹ {{getCashAmountToPayDriver()}}</span>
                </div>

                <!-- Show payment breakdown for partial payment -->
                <div *ngIf="isPartialPaymentAvailable" class="payment-breakdown mt-3">
                    <div class="breakdown-item">
                        <span>Online Payment</span>
                        <span class="t-right">₹ {{getPartialPaymentAmount()}}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Driver Payment</span>
                        <span class="t-right">₹ {{getCashAmountToPayDriver()}}</span>
                    </div>
                </div>
            </div>
        </div>



        <div class="book-now mt-4" (click)="initiatePayment()">Pay Now</div>
    </header>

    <!-- Main Content - Booking Details -->
    <div id="main" class="driverform bookingform">
        <section id="one">
            <header class="major">
                <h2 class="booking-heading">Booking Summary</h2>
            </header>
        </section>

        <div id="two" class="booking-review">
            <span>Traveling & Billing Overview</span>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="booking-invoice">
                        <div class="cabList">
                            <ul>
                                <li>
                                    <div class="cabType">
                                        <img src="{{imageFolderPath}}/car.png">
                                    </div>
                                    <div class="cabInfo">
                                        <div class="modalType">
                                            <span class="d-block">{{bookingDetails.carCategory}}</span>
                                            <small>{{bookingDetails.carFeatures}}</small>
                                        </div>

                                        <div class="bookingDetail">
                                            <span><i class="fa fa-user"></i> &nbsp; {{bookingDetails.carCapacity}} </span>
                                            <span><a class="fa fa-info-circle"></a></span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="add-information">
                            <div class="forminner">
                                <div class="form-group">
                                    <span>
                                        <b>PICK UP ADDRESS</b>
                                        <br>
                                        {{bookingDetails.pickUpAddress}}
                                    </span>
                                </div>
                                <div class="form-group">
                                    <span>
                                        <b>DROP OFF ADDRESS</b>
                                        <br>
                                        {{bookingDetails.dropOffAddress}}
                                    </span>
                                </div>
                                <div class="form-group">
                                    <span>
                                        <b>PICKUP DATE & TIME</b>
                                        <br>
                                        {{bookingDetails.pickUpDate}} at {{bookingDetails.pickUpTime}}
                                    </span>
                                </div>
                                <div class="form-group">
                                    <span>
                                        <b>TRAVELER DETAILS</b>
                                        <br>
                                        {{bookingDetails.travelerName}} - {{bookingDetails.phoneNumber}}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="booking-fare-table">
                        <div class="trip-detail">
                            <span>{{bookingDetails.tripType}} Trip of {{bookingDetails.duration}}.. {{bookingDetails.distance}} Kms</span>
                            <div class="big-heading">₹ {{bookingDetails.totalFare}}</div>
                        </div>

                        <div class="fare-row">
                            <span class="left-col">
                                <div class="b-head">Base Fare</div>
                            </span>
                            <span class="right-col t-right">₹ {{bookingDetails.basicFare}}</span>
                        </div>

                        <div class="fare-row">
                            <span class="left-col">
                                <div class="b-head">Taxes & Fees</div>
                            </span>
                            <span class="right-col t-right">₹ {{bookingDetails.gst}}</span>
                        </div>

                        <div class="fare-row" *ngIf="bookingDetails.tollCharge > 0">
                            <span class="left-col">
                                <div class="b-head">Toll Charges</div>
                            </span>
                            <span class="right-col t-right">₹ {{bookingDetails.tollCharge}}</span>
                        </div>



                        <div class="total-fare">
                            <span class="left-col">
                                <div class="b-head">Total Price</div>
                            </span>
                            <span class="right-col t-right">₹ {{bookingDetails.totalFare}}</span>
                        </div>
                    </div>
                </div>

                <div class="col-sm-12 col-md-12 col-lg-12">
                    <div class="terms-condition">
                        <h2>Terms & Conditions</h2>
                        <p>Parking, Airport Charges would be extra on actual basis and needs to be paid to authorities directly. Toll would be paid as per actual if not paid with booking.
                            Waiting charges @ Rs 200/hr. after 30 mins. (Billed on hourly basis)
                            Balance amount needs to be paid to driver at the beginning of the journey.
                            Only 1 break of 30 mins will be allowed during the journey.
                            AC would be switched off in hilly areas.
                            Any other charges levied be any Govt. body not mentioned under included needs to be paid on actual basis.
                            By confirming the booking you are agreeing with all our terms and conditions.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
