// Anonymous Payment Models for frictionless payment flow

export interface AnonymousBookingDetails {
  bookingId: string;
  tripType: string;
  pickUpCity: string;
  dropOffCity: string;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpDate: string;
  pickUpTime: string;
  carCategory: string;
  carFeatures: string;
  carCapacity: number;
  distance: number;
  duration: string;
  basicFare: number;
  gst: number;
  totalFare: number;
  perKMCharges: string;
  fixRateNote: string;
  tollCharge: number;
  travelerName: string;
  phoneNumber: string;
  mailId: string;
  paymentStatus: string;
  isPaymentPending: boolean;
  cashAmountToPayDriver: number;
  // New fields for partial payment support
  bookingCreatedAt?: string;
  expiresAt?: string;
  paymentType?: string;
  partialPaymentAmount?: number;
  remainingAmountForDriver?: number;
  isPartialPayment?: boolean;
  onlinePaymentAmount?: number;
  driverPaymentAmount?: number;
}

export interface AnonymousBookingDetailsResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: AnonymousBookingDetails;
}

export interface AnonymousPaymentRequest {
  bookingId: string;
  paymentOption: number; // 1 for partial, 2 for full payment
  amountToPay: number;
  callbackUrl: string;
}

export interface AnonymousPaymentTokenResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: {
    tokenUrl: string;
    merchantTransactionId: string;
    orderId: string;
    bookingId: string;
  };
}

export interface AnonymousPaymentVerifyRequest {
  merchantTransactionId: string;
  orderId: string;
  bookingId: string;
}

export interface AnonymousPaymentVerifyResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: {
    paymentStatus: string;
    transactionId: string;
    orderId: string;
    bookingId: string;
    amount: number;
    paymentId: string;
    paymentType: string;
    partialPaymentAmount: number | null;
    remainingAmountForDriver: number | null;
  };
}

export interface AnonymousReceiptData {
  bookingId: string;
  tripType: string;
  pickUpCity: string;
  dropOffCity: string;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpDate: string;
  pickUpTime: string;
  carCategory: string;
  carFeatures: string;
  carCapacity: number;
  distance: number;
  duration: string;
  basicFare: number;
  gst: number;
  totalFare: number;
  paymentType: string;
  paymentStatus: string;
  amountPaid: number;
  remainingAmountForDriver: number;
  travelerName: string;
  phoneNumber: string;
  mailId: string;
  transactionId: string;
  paymentId: string;
  paymentDate: string;
}

export interface AnonymousReceiptResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: AnonymousReceiptData;
}



// Payment options enum
export enum AnonymousPaymentOption {
  PARTIAL = 1,
  FULL = 2
}

// Payment status enum
export enum AnonymousPaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}
